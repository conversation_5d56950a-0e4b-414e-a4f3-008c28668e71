# 登录时长延长方案

## 📋 方案概述

本方案为医疗设备管理系统提供了完整的登录时长管理解决方案，支持30天登录时长，并具备自动延长、状态监控、管理配置等功能。

## 🎯 核心功能

### 1. 30天登录时长
- ✅ Session过期时间：30天（2,592,000秒）
- ✅ Cookie生命周期：30天
- ✅ 记住登录功能：90天
- ✅ 数据库Session存储：支持长期存储

### 2. 自动延长机制
- ✅ 智能检测：剩余时间少于7天时自动延长
- ✅ 用户活动监控：基于用户操作自动触发
- ✅ 后台定时检查：每5分钟检查一次Session状态
- ✅ 无感知延长：用户无需手动操作

### 3. 状态监控
- ✅ 实时状态显示：剩余时间、过期日期
- ✅ 前端监控：JavaScript自动监控登录状态
- ✅ 警告提醒：临近过期时主动提醒用户
- ✅ 在线统计：活跃用户数、今日登录数

### 4. 管理功能
- ✅ 配置界面：管理员可调整登录时长设置
- ✅ 手动延长：用户可主动延长登录时间
- ✅ 批量清理：清理过期Session
- ✅ 日志记录：记录所有登录相关操作

## 🔧 技术实现

### 1. 配置文件优化
**文件**: `Application/Common/Conf/config.php`

```php
'SESSION_TYPE'           => 'mysqli',
'SESSION_EXPIRE'         => 2592000, // 30天
'SESSION_OPTIONS'        => [
    'expire' => 2592000,
    'cookie_lifetime' => 2592000,
    'cache_expire' => 2592000,
    'cookie_httponly' => true,
    'cookie_samesite' => 'Lax',
],
'LOGIN_SESSION_EXPIRE'   => 2592000,
'AUTO_EXTEND_SESSION'    => true,
'SESSION_EXTEND_THRESHOLD' => 604800, // 7天
```

### 2. Session管理类
**文件**: `Application/Common/Common/SessionManager.class.php`

核心方法：
- `checkAndExtendSession()` - 检查并自动延长Session
- `extendSession()` - 手动延长Session
- `setRememberCookie()` - 设置记住登录
- `validateRememberCookie()` - 验证记住登录
- `getSessionRemainingTime()` - 获取剩余时间
- `cleanExpiredSessions()` - 清理过期Session

### 3. 控制器接口
**文件**: `Application/Admin/Controller/Login/SessionExtendController.class.php`

API接口：
- `/A/Login/SessionExtend/checkAndExtend` - 检查并延长Session
- `/A/Login/SessionExtend/extendSession` - 手动延长Session
- `/A/Login/SessionExtend/getLoginStatus` - 获取登录状态
- `/A/Login/SessionExtend/index` - 管理界面

### 4. 前端监控
**文件**: `Public/js/session.manager.js`

功能特性：
- 定时检查Session状态（5分钟间隔）
- 用户活动监听
- 自动延长提醒
- 状态实时更新

### 5. 管理界面
**文件**: `Application/Admin/View/Login/session_config.html`

管理功能：
- 登录时长配置
- 在线用户统计
- Session状态监控
- 批量操作管理

## 🚀 部署步骤

### 1. 兼容性检查
首先运行兼容性检查脚本：
```bash
php check_compatibility.php
```

确保系统满足以下要求：
- PHP 5.4.0+ (推荐PHP 7.0+)
- mysqli扩展已启用
- session扩展已启用
- 足够的内存限制(建议128M+)

### 2. 文件部署
确保以下文件已正确部署：
- `Application/Common/Conf/config.php` (已更新)
- `Application/Common/Common/SessionManager.class.php` (新增)
- `Application/Admin/Controller/Login/SessionExtendController.class.php` (新增)
- `Application/Admin/View/Login/session_config.html` (新增)
- `Public/js/session.manager.js` (新增)
- `check_compatibility.php` (兼容性检查脚本)

### 3. 数据库检查
确保Session表存在：
```sql
CREATE TABLE sb_session (
  session_id varchar(255) NOT NULL,
  session_expire int(11) NOT NULL,
  session_data blob,
  UNIQUE KEY `session_id` (`session_id`)
);
```

确保用户表包含必要字段：
```sql
ALTER TABLE sb_user ADD COLUMN identifier VARCHAR(32) DEFAULT NULL COMMENT '登录标识';
ALTER TABLE sb_user ADD COLUMN token VARCHAR(32) DEFAULT NULL COMMENT '登录标识';
ALTER TABLE sb_user ADD COLUMN timeout int(11) unsigned DEFAULT NULL COMMENT '登录cookie过期时间';
ALTER TABLE sb_user ADD COLUMN last_activity int(11) DEFAULT NULL COMMENT '最后活动时间';
```

### 4. 配置验证
运行测试脚本验证配置：
```bash
php test_login_duration.php
```

### 5. 前端集成
在需要监控的页面添加：
```html
<script src="/Public/js/session.manager.js"></script>
<body class="admin-page" data-session-monitor="true">
```

## 📊 使用说明

### 管理员操作

1. **访问管理界面**
   - URL: `/A/Login/SessionExtend/index`
   - 需要超级管理员权限

2. **配置登录时长**
   - Session过期时间：1-90天
   - 记住登录时间：1-365天
   - 自动延长开关：启用/禁用
   - 延长阈值：1-30天

3. **监控在线状态**
   - 查看活跃Session数量
   - 查看今日登录用户数
   - 清理过期Session

### 用户操作

1. **查看登录状态**
   - 剩余登录时间
   - 过期日期
   - 最后活动时间

2. **手动延长登录**
   - 点击"延长登录时间"按钮
   - 系统自动延长30天

3. **记住登录**
   - 登录时勾选"记住密码"
   - 90天内免密码登录

## 🔒 安全考虑

### 1. Cookie安全
- HttpOnly：防止XSS攻击
- SameSite：防止CSRF攻击
- 加密存储：identifier和token加密

### 2. Session安全
- 数据库存储：避免文件系统风险
- 定期清理：自动清理过期Session
- 活动监控：记录用户活动时间

### 3. 权限控制
- 管理功能需要超级管理员权限
- API接口验证登录状态
- 操作日志记录

## 🐛 故障排除

### 1. PHP语法错误
如果遇到"syntax error, unexpected '?'"等错误：
- 检查PHP版本是否支持相关语法
- 运行兼容性检查：`php check_compatibility.php`
- 确保使用了兼容的数组语法：`array()` 而不是 `[]`
- 确保使用了兼容的null检查：`isset($var) ? $var : default` 而不是 `$var ?? default`

### 2. Session不能延长
- 检查数据库连接
- 验证Session表结构
- 确认配置文件设置

### 3. 前端监控不工作
- 检查JavaScript文件加载
- 确认页面标记正确
- 查看浏览器控制台错误

### 4. 记住登录失败
- 检查Cookie设置
- 验证用户表字段
- 确认加密密钥配置

## 📈 性能优化

### 1. 数据库优化
- Session表添加索引
- 定期清理过期数据
- 考虑分表策略

### 2. 缓存策略
- 用户状态缓存
- Session信息缓存
- 减少数据库查询

### 3. 前端优化
- 合理设置检查间隔
- 避免频繁API调用
- 使用事件驱动更新

## 📞 技术支持

如有问题，请检查：
1. 运行测试脚本：`php test_login_duration.php`
2. 查看系统日志：`Application/Runtime/Logs/`
3. 检查浏览器控制台错误
4. 验证数据库连接和表结构

---

**版本**: 1.0  
**更新日期**: 2024-07-01  
**兼容性**: ThinkPHP 3.2+, MySQL 5.5+
