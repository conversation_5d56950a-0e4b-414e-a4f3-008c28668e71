<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音状态测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .voice-control {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .voice-btn {
            background: #24B5F8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .voice-status-icon {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #666;
            display: inline-block;
        }
        
        .status-text {
            font-size: 12px;
            background: rgba(0,0,0,0.1);
            padding: 5px 10px;
            border-radius: 3px;
        }
        
        /* 状态样式 */
        .voice-status-inactive { background: #666 !important; }
        .voice-status-active { background: #00FF00 !important; box-shadow: 0 0 5px #00FF00; }
        .voice-status-playing { 
            background: #FFD700 !important; 
            animation: voice-pulse 1s infinite;
            box-shadow: 0 0 8px #FFD700;
        }
        .voice-status-error { background: #FF4444 !important; box-shadow: 0 0 5px #FF4444; }
        
        @keyframes voice-pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .voice-btn-inactive { background: #666 !important; }
        .voice-btn-active { background: #00AA00 !important; }
        .voice-btn-playing { background: #FF8C00 !important; }
        .voice-btn-error { background: #CC0000 !important; }
        
        .debug-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-buttons {
            margin: 20px 0;
        }
        
        .test-buttons button {
            margin: 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>语音状态指示器测试</h1>
        
        <div class="voice-control">
            <button id="voiceBtn" class="voice-btn">
                <span id="voiceStatusIcon" class="voice-status-icon voice-status-inactive"></span>
                <span id="voiceButtonText">启动语音提示</span>
            </button>
            <span id="voiceStatusText" class="status-text">未启动</span>
        </div>
        
        <div class="test-buttons">
            <h3>状态测试按钮：</h3>
            <button onclick="testStatus('inactive')" style="background: #666; color: white;">设为未启动</button>
            <button onclick="testStatus('active')" style="background: #00AA00; color: white;">设为已启动</button>
            <button onclick="testStatus('playing')" style="background: #FF8C00; color: white;">设为播放中</button>
            <button onclick="testStatus('error')" style="background: #CC0000; color: white;">设为错误</button>
            <button onclick="testVoice()" style="background: #007bff; color: white;">测试语音播放</button>
        </div>
        
        <div class="debug-info">
            <h3>调试信息：</h3>
            <div id="debugInfo">等待初始化...</div>
        </div>
    </div>

    <script src="Public/js/voice.prompt.js"></script>
    <script>
        // 状态UI更新函数
        function updateVoiceStatusUI(status) {
            const statusIcon = document.getElementById('voiceStatusIcon');
            const buttonText = document.getElementById('voiceButtonText');
            const statusText = document.getElementById('voiceStatusText');
            const button = document.getElementById('voiceBtn');
            
            // 清除所有状态类
            statusIcon.className = 'voice-status-icon';
            button.className = 'voice-btn';
            
            switch(status) {
                case 'inactive':
                    statusIcon.classList.add('voice-status-inactive');
                    button.classList.add('voice-btn-inactive');
                    buttonText.textContent = '启动语音提示';
                    statusText.textContent = '未启动';
                    break;
                case 'active':
                    statusIcon.classList.add('voice-status-active');
                    button.classList.add('voice-btn-active');
                    buttonText.textContent = '语音已启动';
                    statusText.textContent = '已启动';
                    break;
                case 'playing':
                    statusIcon.classList.add('voice-status-playing');
                    button.classList.add('voice-btn-playing');
                    buttonText.textContent = '语音播放中';
                    statusText.textContent = '播放中';
                    break;
                case 'error':
                    statusIcon.classList.add('voice-status-error');
                    button.classList.add('voice-btn-error');
                    buttonText.textContent = '语音错误';
                    statusText.textContent = '错误';
                    break;
            }
            
            updateDebugInfo();
        }

        // 更新调试信息
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            const status = typeof window.getVoiceStatus === 'function' ? window.getVoiceStatus() : '未知';
            const supported = 'speechSynthesis' in window;
            
            debugDiv.innerHTML = `
                当前状态: ${status}<br>
                浏览器支持: ${supported ? '是' : '否'}<br>
                speakPrompt函数: ${typeof window.speakPrompt === 'function' ? '已加载' : '未加载'}<br>
                getVoiceStatus函数: ${typeof window.getVoiceStatus === 'function' ? '已加载' : '未加载'}<br>
                时间: ${new Date().toLocaleString()}
            `;
        }

        // 测试状态切换
        function testStatus(status) {
            if (typeof window.setVoiceStatus === 'function') {
                window.setVoiceStatus(status);
            } else {
                updateVoiceStatusUI(status);
            }
        }

        // 测试语音播放
        function testVoice() {
            if (typeof window.speakPrompt === 'function') {
                window.speakPrompt('这是一个语音测试');
            } else {
                alert('语音功能未加载');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            
            // 添加状态监听器
            if (typeof window.addStatusListener === 'function') {
                window.addStatusListener(function(newStatus, oldStatus) {
                    console.log('状态变化:', oldStatus, '->', newStatus);
                    updateVoiceStatusUI(newStatus);
                });
            }
            
            // 添加按钮点击事件
            document.getElementById('voiceBtn').addEventListener('click', function() {
                testVoice();
            });
            
            // 初始化显示
            setTimeout(function() {
                const currentStatus = typeof window.getVoiceStatus === 'function' ? window.getVoiceStatus() : 'inactive';
                updateVoiceStatusUI(currentStatus);
            }, 500);
            
            // 定期更新调试信息
            setInterval(updateDebugInfo, 2000);
        });
    </script>
</body>
</html>
