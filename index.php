<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2014 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
ini_set("session.cookie_httponly", 1);
// 设置session超时时间为30天
ini_set("session.gc_maxlifetime", 10368000);
ini_set("session.cookie_lifetime", 10368000);
session_set_cookie_params(10368000);
session_cache_expire(10368000);

// 应用入口文件

// 检测PHP环境
if(version_compare(PHP_VERSION,'5.3.0','<'))  die('require PHP > 5.3.0 !');

// 开启调试模式 建议开发阶段开启 部署阶段注释或者设为false
define('APP_DEBUG',true);

//是否显示报错信息（2019.05.10新增参数）
define('showError',true);

// 定义应用目录
define('APP_PATH','./Application/');
define('APP_PUBLIC','./Public/');

define('SRC_PATH','sbsys');

// 引入ThinkPHP入口文件
require 'ThinkPHP/ThinkPHP.php';

// 亲^_^ 后面不需要任何代码了 就是如此简单
