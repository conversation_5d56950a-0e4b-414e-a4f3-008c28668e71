<?php
/**
 * PHP版本兼容性检查脚本
 * 检查登录时长方案的兼容性
 */

echo "=== PHP版本兼容性检查 ===\n\n";

// 1. 检查PHP版本
echo "1. PHP版本检查:\n";
echo "当前PHP版本: " . PHP_VERSION . "\n";

if (version_compare(PHP_VERSION, '5.4.0', '>=')) {
    echo "✓ PHP版本支持数组短语法\n";
} else {
    echo "⚠ PHP版本较低，需要使用array()语法\n";
}

if (version_compare(PHP_VERSION, '7.0.0', '>=')) {
    echo "✓ PHP版本支持null合并操作符(??)\n";
} else {
    echo "⚠ PHP版本不支持null合并操作符，需要使用isset()三元操作符\n";
}

if (version_compare(PHP_VERSION, '5.6.0', '>=')) {
    echo "✓ PHP版本支持可变参数\n";
} else {
    echo "⚠ PHP版本不支持可变参数\n";
}
echo "\n";

// 2. 检查关键文件语法
echo "2. 关键文件语法检查:\n";

$files = array(
    'Application/Common/Common/SessionManager.class.php',
    'Application/Admin/Controller/Login/SessionExtendController.class.php',
    'Application/Common/Conf/config.php'
);

foreach ($files as $file) {
    if (file_exists($file)) {
        $output = array();
        $return_var = 0;
        exec("php -l " . escapeshellarg($file) . " 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "✓ {$file}: 语法正确\n";
        } else {
            echo "✗ {$file}: 语法错误\n";
            echo "  错误信息: " . implode("\n  ", $output) . "\n";
        }
    } else {
        echo "⚠ {$file}: 文件不存在\n";
    }
}
echo "\n";

// 3. 检查必要的PHP扩展
echo "3. PHP扩展检查:\n";

$required_extensions = array(
    'mysqli' => 'MySQL数据库连接',
    'session' => 'Session支持',
    'json' => 'JSON处理',
    'hash' => '哈希函数',
    'openssl' => 'SSL加密（可选）'
);

foreach ($required_extensions as $ext => $desc) {
    if (extension_loaded($ext)) {
        echo "✓ {$ext}: 已加载 ({$desc})\n";
    } else {
        echo "✗ {$ext}: 未加载 ({$desc})\n";
    }
}
echo "\n";

// 4. 检查Session配置
echo "4. Session配置检查:\n";

$session_configs = array(
    'session.save_handler' => '存储处理器',
    'session.gc_maxlifetime' => '垃圾回收最大生命周期',
    'session.cookie_lifetime' => 'Cookie生命周期',
    'session.use_cookies' => '使用Cookie',
    'session.cookie_httponly' => 'HttpOnly Cookie'
);

foreach ($session_configs as $config => $desc) {
    $value = ini_get($config);
    echo "{$config}: {$value} ({$desc})\n";
}
echo "\n";

// 5. 检查内存和时间限制
echo "5. 系统限制检查:\n";
echo "内存限制: " . ini_get('memory_limit') . "\n";
echo "执行时间限制: " . ini_get('max_execution_time') . " 秒\n";
echo "上传文件大小限制: " . ini_get('upload_max_filesize') . "\n";
echo "POST数据大小限制: " . ini_get('post_max_size') . "\n";
echo "\n";

// 6. 检查时区设置
echo "6. 时区设置检查:\n";
echo "默认时区: " . date_default_timezone_get() . "\n";
echo "当前时间: " . date('Y-m-d H:i:s') . "\n";
echo "时间戳: " . time() . "\n";
echo "\n";

// 7. 生成兼容性报告
echo "7. 兼容性总结:\n";

$php_version_ok = version_compare(PHP_VERSION, '5.4.0', '>=');
$mysqli_ok = extension_loaded('mysqli');
$session_ok = extension_loaded('session');

if ($php_version_ok && $mysqli_ok && $session_ok) {
    echo "✓ 系统满足登录时长方案的基本要求\n";
    echo "✓ 可以正常部署和运行\n";
    
    if (version_compare(PHP_VERSION, '7.0.0', '<')) {
        echo "⚠ 建议升级到PHP 7.0+以获得更好的性能\n";
    }
} else {
    echo "✗ 系统不满足基本要求，需要解决以下问题:\n";
    
    if (!$php_version_ok) {
        echo "  - 升级PHP版本到5.4.0或更高\n";
    }
    if (!$mysqli_ok) {
        echo "  - 安装mysqli扩展\n";
    }
    if (!$session_ok) {
        echo "  - 启用session扩展\n";
    }
}

echo "\n=== 检查完成 ===\n";
echo "如果所有检查都通过，可以继续部署登录时长方案。\n";
?>
