<?php
/**
 * 登录时长测试脚本
 * 用于验证登录时长设置是否正确
 */

// 引入ThinkPHP框架
require_once './ThinkPHP/ThinkPHP.php';

echo "=== 登录时长配置测试 ===\n\n";

// 1. 检查配置文件中的设置
echo "1. 检查配置文件设置:\n";
$sessionExpire = C('SESSION_EXPIRE');
$loginSessionExpire = C('LOGIN_SESSION_EXPIRE');
$loginCookieExpire = C('LOGIN_COOKIE_EXPIRE');
$rememberLoginExpire = C('REMEMBER_LOGIN_EXPIRE');
$autoExtendSession = C('AUTO_EXTEND_SESSION');
$sessionExtendThreshold = C('SESSION_EXTEND_THRESHOLD');

echo "SESSION_EXPIRE: " . $sessionExpire . " 秒 (" . round($sessionExpire/86400, 1) . " 天)\n";
echo "LOGIN_SESSION_EXPIRE: " . $loginSessionExpire . " 秒 (" . round($loginSessionExpire/86400, 1) . " 天)\n";
echo "LOGIN_COOKIE_EXPIRE: " . $loginCookieExpire . " 秒 (" . round($loginCookieExpire/86400, 1) . " 天)\n";
echo "REMEMBER_LOGIN_EXPIRE: " . $rememberLoginExpire . " 秒 (" . round($rememberLoginExpire/86400, 1) . " 天)\n";
echo "AUTO_EXTEND_SESSION: " . ($autoExtendSession ? '启用' : '禁用') . "\n";
echo "SESSION_EXTEND_THRESHOLD: " . $sessionExtendThreshold . " 秒 (" . round($sessionExtendThreshold/86400, 1) . " 天)\n\n";

// 2. 检查Session配置选项
echo "2. 检查Session配置选项:\n";
$sessionOptions = C('SESSION_OPTIONS');
foreach ($sessionOptions as $key => $value) {
    if (in_array($key, ['expire', 'cookie_lifetime', 'cache_expire'])) {
        echo "{$key}: {$value} 秒 (" . round($value/86400, 1) . " 天)\n";
    } else {
        echo "{$key}: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
    }
}
echo "\n";

// 3. 检查数据库连接和Session表
echo "3. 检查数据库和Session表:\n";
try {
    $model = new \Think\Model();
    $sessionTable = C('SESSION_TABLE') ?: C("DB_PREFIX") . "session";
    echo "Session表名: {$sessionTable}\n";
    
    // 检查表是否存在
    $tables = $model->query("SHOW TABLES LIKE '{$sessionTable}'");
    if ($tables) {
        echo "Session表存在: 是\n";
        
        // 检查表结构
        $structure = $model->query("DESCRIBE {$sessionTable}");
        echo "表结构:\n";
        foreach ($structure as $field) {
            echo "  - {$field['Field']}: {$field['Type']}\n";
        }
        
        // 检查当前Session数量
        $count = $model->query("SELECT COUNT(*) as count FROM {$sessionTable}");
        echo "当前Session总数: " . $count[0]['count'] . "\n";
        
        // 检查活跃Session数量
        $activeCount = $model->query("SELECT COUNT(*) as count FROM {$sessionTable} WHERE session_expire > " . time());
        echo "活跃Session数: " . $activeCount[0]['count'] . "\n";
        
        // 检查过期Session数量
        $expiredCount = $model->query("SELECT COUNT(*) as count FROM {$sessionTable} WHERE session_expire <= " . time());
        echo "过期Session数: " . $expiredCount[0]['count'] . "\n";
        
    } else {
        echo "Session表存在: 否\n";
        echo "需要创建Session表，建议SQL:\n";
        echo "CREATE TABLE {$sessionTable} (\n";
        echo "  session_id varchar(255) NOT NULL,\n";
        echo "  session_expire int(11) NOT NULL,\n";
        echo "  session_data blob,\n";
        echo "  UNIQUE KEY `session_id` (`session_id`)\n";
        echo ");\n";
    }
} catch (Exception $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. 检查用户表中的登录相关字段
echo "4. 检查用户表登录字段:\n";
try {
    $userTable = C("DB_PREFIX") . "user";
    $userStructure = $model->query("DESCRIBE {$userTable}");
    
    $loginFields = ['identifier', 'token', 'timeout', 'logintime', 'last_activity', 'expire_time'];
    echo "用户表登录相关字段:\n";
    
    foreach ($userStructure as $field) {
        if (in_array($field['Field'], $loginFields)) {
            echo "  ✓ {$field['Field']}: {$field['Type']}\n";
        }
    }
    
    // 检查是否有用户设置了记住登录
    $rememberCount = $model->query("SELECT COUNT(*) as count FROM {$userTable} WHERE identifier IS NOT NULL AND token IS NOT NULL");
    echo "设置记住登录的用户数: " . $rememberCount[0]['count'] . "\n";
    
} catch (Exception $e) {
    echo "检查用户表失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. 测试SessionManager类
echo "5. 测试SessionManager类:\n";
try {
    // 检查类是否可以加载
    if (class_exists('\Common\Common\SessionManager')) {
        echo "SessionManager类: 可用\n";
        
        // 测试获取Session信息
        $sessionInfo = \Common\Common\SessionManager::getSessionInfo();
        if ($sessionInfo) {
            echo "当前Session信息: 可获取\n";
            echo "Session过期时间: " . date('Y-m-d H:i:s', $sessionInfo['session_expire']) . "\n";
        } else {
            echo "当前Session信息: 无法获取（可能未登录）\n";
        }
        
        // 测试获取剩余时间
        $remainingTime = \Common\Common\SessionManager::getSessionRemainingTime();
        echo "Session剩余时间: {$remainingTime} 秒\n";
        
        // 测试在线统计
        $stats = \Common\Common\SessionManager::getOnlineStats();
        echo "在线统计功能: 可用\n";
        echo "  - 活跃Session: " . $stats['active_sessions'] . "\n";
        echo "  - 今日登录: " . $stats['today_logins'] . "\n";
        
    } else {
        echo "SessionManager类: 不可用\n";
    }
} catch (Exception $e) {
    echo "SessionManager测试失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. 生成配置建议
echo "6. 配置建议:\n";

$currentSessionExpire = C('SESSION_EXPIRE') ?: C('LOGIN_SESSION_EXPIRE');
$targetExpire = 30 * 24 * 3600; // 30天

if ($currentSessionExpire == $targetExpire) {
    echo "✓ Session过期时间已正确设置为30天\n";
} else {
    echo "⚠ Session过期时间需要调整:\n";
    echo "  当前: " . round($currentSessionExpire/86400, 1) . " 天\n";
    echo "  建议: 30 天\n";
}

$autoExtend = C('AUTO_EXTEND_SESSION');
if ($autoExtend) {
    echo "✓ 自动延长Session已启用\n";
} else {
    echo "⚠ 建议启用自动延长Session功能\n";
}

$threshold = C('SESSION_EXTEND_THRESHOLD');
if ($threshold && $threshold <= 7 * 24 * 3600) {
    echo "✓ Session延长阈值设置合理\n";
} else {
    echo "⚠ 建议设置Session延长阈值为7天或更少\n";
}

echo "\n=== 测试完成 ===\n";
echo "如需访问管理界面，请访问: /A/Login/SessionExtend/index\n";
?>
