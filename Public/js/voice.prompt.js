let hasUserInteraction = false;
let initialPlayAttempted = false;
let speechSynthesisSupported = false;
let voiceStatus = 'inactive'; // 语音状态: inactive, active, playing, error
let statusCallbacks = []; // 状态变化回调函数列表

// 状态管理函数
function setVoiceStatus(status) {
    const oldStatus = voiceStatus;
    voiceStatus = status;
    console.log('语音状态变化:', oldStatus, '->', status);

    // 触发状态变化回调
    statusCallbacks.forEach(callback => {
        try {
            callback(status, oldStatus);
        } catch (error) {
            console.error('状态回调执行错误:', error);
        }
    });
}

// 添加状态变化监听器
function addStatusListener(callback) {
    if (typeof callback === 'function') {
        statusCallbacks.push(callback);
    }
}

// 获取当前语音状态
function getVoiceStatus() {
    return voiceStatus;
}

// 检查浏览器支持
function checkSpeechSynthesisSupport() {
    speechSynthesisSupported = 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;
    console.log('语音合成支持状态:', speechSynthesisSupported);

    if (!speechSynthesisSupported) {
        setVoiceStatus('error');
    }

    return speechSynthesisSupported;
}

// 语音提示函数（核心功能）
function speakPrompt(text, options = {}) {
    console.log('speakPrompt被调用，文本:', text);
    console.log('当前时间:', new Date().toLocaleString());

    // 检查浏览器支持
    if (!checkSpeechSynthesisSupport()) {
        console.warn('浏览器不支持语音合成功能');
        return false;
    }

    const synth = window.speechSynthesis;

    // 如果正在播放，则先停止
    if (synth.speaking) {
        console.log('停止当前播放的语音');
        synth.cancel();
    }

    if (!text || text.trim() === '') {
        console.warn('语音文本为空');
        return false;
    }

    try {
        const utterance = new SpeechSynthesisUtterance(text.trim());

        // 设置语音参数
        utterance.rate = options.rate || 1;
        utterance.pitch = options.pitch || 1;
        utterance.volume = options.volume || 1;
        utterance.lang = options.lang || 'zh-CN';

        // 设置回调
        utterance.onstart = function () {
            console.log('语音开始播放:', text);
            setVoiceStatus('playing');
        };

        utterance.onend = function () {
            console.log('语音播放完成:', text);
            setVoiceStatus('active');
        };

        utterance.onerror = function (event) {
            console.error('语音播放错误:', event.error, '文本:', text);
            setVoiceStatus('error');
            if (event.error === 'not-allowed') {
                console.warn('语音播放被阻止，可能需要用户交互');
                // 尝试重新播放
                setTimeout(function () {
                    console.log('尝试重新播放语音...');
                    synth.speak(utterance);
                }, 1000);
            }
        };

        // 尝试播放语音
        synth.speak(utterance);
        initialPlayAttempted = true;
        console.log('语音播放请求已发送');
        return true;
    } catch (error) {
        console.error('语音播放异常:', error);
        return false;
    }
}

// 显示权限提示模态框
function showPermissionModal() {
    const modal = document.getElementById('permission-modal');
    if (modal) {
        modal.classList.remove('opacity-0', 'pointer-events-none');
        modal.querySelector('.modal').classList.remove('scale-95');
        modal.querySelector('.modal').classList.add('scale-100');
    }
}

// 隐藏权限提示模态框
function hidePermissionModal() {
    const modal = document.getElementById('permission-modal');
    if (modal) {
        modal.classList.add('opacity-0', 'pointer-events-none');
        modal.querySelector('.modal').classList.remove('scale-100');
        modal.querySelector('.modal').classList.add('scale-95');
    }
}

// 尝试自动播放初始语音
function tryInitialPlay() {
    if (initialPlayAttempted) return;

    try {
        console.log('尝试播放初始语音');
        speakPrompt('欢迎使用语音提示系统');
    } catch (error) {
        console.error('初始语音播放失败:', error);
        // 如果失败，显示权限提示
        showPermissionModal();
    }
}

// 用户交互后启用语音
function enableVoiceAfterInteraction() {
    hasUserInteraction = true;
    console.log('用户交互已检测，语音功能已启用');
    hidePermissionModal();
    setVoiceStatus('active');

    // 用户交互后尝试播放测试语音
    setTimeout(function () {
        console.log('用户交互后尝试播放测试语音');
        speakPrompt('语音功能已激活');
    }, 500);
}

// 页面加载完成后尝试自动播放
document.addEventListener('DOMContentLoaded', function () {
    console.log('页面加载完成，初始化语音系统');
    checkSpeechSynthesisSupport();

    // 添加用户交互监听
    ['click', 'touchstart', 'keydown'].forEach(eventType => {
        document.addEventListener(eventType, enableVoiceAfterInteraction, { once: true });
    });

    // 延迟尝试初始播放
    setTimeout(tryInitialPlay, 1000);
});

// 导出函数供全局使用
window.speakPrompt = speakPrompt;
window.getVoiceStatus = getVoiceStatus;
window.addStatusListener = addStatusListener;
window.setVoiceStatus = setVoiceStatus;

// 添加全局调试函数
window.debugVoice = function () {
    console.log('=== 语音功能调试信息 ===');
    console.log('hasUserInteraction:', hasUserInteraction);
    console.log('initialPlayAttempted:', initialPlayAttempted);
    console.log('speechSynthesisSupported:', speechSynthesisSupported);
    console.log('speechSynthesis存在:', 'speechSynthesis' in window);
    console.log('SpeechSynthesisUtterance存在:', 'SpeechSynthesisUtterance' in window);
    if ('speechSynthesis' in window) {
        console.log('speechSynthesis状态:', window.speechSynthesis.speaking ? '正在播放' : '未播放');
    }
    console.log('speakPrompt函数存在:', typeof speakPrompt === 'function');
    console.log('window.speakPrompt存在:', typeof window.speakPrompt === 'function');
    console.log('=======================');
};