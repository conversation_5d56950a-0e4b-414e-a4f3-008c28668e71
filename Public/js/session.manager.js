/**
 * Session管理器
 * 用于前端监控和管理登录状态
 */
class SessionManager {
    constructor(options = {}) {
        this.options = {
            checkInterval: 5 * 60 * 1000, // 5分钟检查一次
            warningThreshold: 7 * 24 * 3600, // 7天警告阈值
            autoExtend: true, // 是否自动延长
            showNotifications: true, // 是否显示通知
            ...options
        };
        
        this.checkTimer = null;
        this.lastCheckTime = 0;
        this.sessionData = null;
        
        this.init();
    }
    
    /**
     * 初始化Session管理器
     */
    init() {
        // 页面加载时立即检查一次
        this.checkSessionStatus();
        
        // 设置定时检查
        this.startPeriodicCheck();
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // 页面变为可见时检查Session
                this.checkSessionStatus();
            }
        });
        
        // 监听用户活动
        this.bindUserActivityEvents();
    }
    
    /**
     * 开始定期检查
     */
    startPeriodicCheck() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
        }
        
        this.checkTimer = setInterval(() => {
            this.checkSessionStatus();
        }, this.options.checkInterval);
    }
    
    /**
     * 停止定期检查
     */
    stopPeriodicCheck() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
    }
    
    /**
     * 检查Session状态
     */
    async checkSessionStatus() {
        try {
            const response = await this.makeRequest('/A/Login/SessionExtend/checkAndExtend');
            
            if (response.status === 1) {
                this.sessionData = response.data;
                this.handleSessionData(response.data);
            } else if (response.need_login) {
                this.handleSessionExpired();
            }
        } catch (error) {
            console.error('检查Session状态失败:', error);
        }
    }
    
    /**
     * 处理Session数据
     */
    handleSessionData(data) {
        const { remaining_time, remaining_days, extended } = data;
        
        // 如果Session被延长，显示通知
        if (extended && this.options.showNotifications) {
            this.showNotification('登录时间已自动延长', 'success');
        }
        
        // 如果剩余时间少于警告阈值，显示警告
        if (remaining_time < this.options.warningThreshold && this.options.showNotifications) {
            this.showSessionWarning(remaining_days);
        }
        
        // 更新页面上的Session信息显示
        this.updateSessionDisplay(data);
    }
    
    /**
     * 处理Session过期
     */
    handleSessionExpired() {
        this.stopPeriodicCheck();
        
        if (this.options.showNotifications) {
            this.showNotification('登录已过期，请重新登录', 'error');
        }
        
        // 延迟跳转到登录页面
        setTimeout(() => {
            window.location.href = '/A/Login/login';
        }, 2000);
    }
    
    /**
     * 显示Session警告
     */
    showSessionWarning(remainingDays) {
        const message = `您的登录将在 ${remainingDays} 天后过期，是否延长登录时间？`;
        
        if (confirm(message)) {
            this.extendSession();
        }
    }
    
    /**
     * 手动延长Session
     */
    async extendSession() {
        try {
            const response = await this.makeRequest('/A/Login/SessionExtend/extendSession', 'POST');
            
            if (response.status === 1) {
                this.sessionData = response.data;
                this.showNotification('登录时间延长成功', 'success');
                this.updateSessionDisplay(response.data);
            } else {
                this.showNotification('登录时间延长失败', 'error');
            }
        } catch (error) {
            console.error('延长Session失败:', error);
            this.showNotification('延长登录时间时发生错误', 'error');
        }
    }
    
    /**
     * 获取登录状态信息
     */
    async getLoginStatus() {
        try {
            const response = await this.makeRequest('/A/Login/SessionExtend/getLoginStatus');
            return response;
        } catch (error) {
            console.error('获取登录状态失败:', error);
            return null;
        }
    }
    
    /**
     * 更新页面上的Session显示
     */
    updateSessionDisplay(data) {
        // 更新剩余时间显示
        const remainingTimeElement = document.getElementById('session-remaining-time');
        if (remainingTimeElement) {
            remainingTimeElement.textContent = `${data.remaining_days} 天`;
        }
        
        // 更新过期时间显示
        const expireDateElement = document.getElementById('session-expire-date');
        if (expireDateElement) {
            expireDateElement.textContent = data.expire_date;
        }
        
        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('sessionUpdated', {
            detail: data
        }));
    }
    
    /**
     * 绑定用户活动事件
     */
    bindUserActivityEvents() {
        const events = ['click', 'keypress', 'scroll', 'mousemove'];
        let lastActivity = Date.now();
        
        const updateActivity = () => {
            const now = Date.now();
            if (now - lastActivity > 60000) { // 1分钟内只记录一次
                lastActivity = now;
                // 可以在这里发送用户活动记录到服务器
            }
        };
        
        events.forEach(event => {
            document.addEventListener(event, updateActivity, { passive: true });
        });
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 如果页面有layer组件，使用layer
        if (typeof layer !== 'undefined') {
            const icon = type === 'success' ? 1 : type === 'error' ? 2 : 0;
            layer.msg(message, { icon: icon, time: 3000 });
            return;
        }
        
        // 否则使用浏览器原生通知
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('登录状态提醒', {
                body: message,
                icon: '/Public/images/logo.png'
            });
        } else {
            // 降级到alert
            alert(message);
        }
    }
    
    /**
     * 发送请求
     */
    async makeRequest(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        return await response.json();
    }
    
    /**
     * 销毁Session管理器
     */
    destroy() {
        this.stopPeriodicCheck();
        // 移除事件监听器等清理工作
    }
}

// 全局Session管理器实例
window.sessionManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 只在需要的页面初始化Session管理器
    if (document.body.classList.contains('admin-page') || 
        document.querySelector('[data-session-monitor="true"]')) {
        
        window.sessionManager = new SessionManager({
            checkInterval: 5 * 60 * 1000, // 5分钟
            warningThreshold: 7 * 24 * 3600, // 7天
            autoExtend: true,
            showNotifications: true
        });
    }
});

// 导出SessionManager类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionManager;
}
