# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

open_file_label=Tuide
print.title=Rábe fɨnoraɨma
print_label=Rábe fɨnoraɨma
download.title=Yúnua
download_label=<PERSON>únua
bookmark.title=B<PERSON>rui éroika (kómue éroirafo tuño fakayena)
bookmark_label=Bírui éroika

# Secondary toolbar and context menu
tools.title=Ránɨaɨ táɨjɨyena
tools_label=Ránɨaɨ táɨjɨyena
first_page.title=Nano fueñe rabemo jaíri
first_page.label=Nano fueñe rabemo jaíri
first_page_label=Nano fueñe rabemo jaíri
last_page.title=Ɨ́kóɨ fueñe rabemo jaíri
last_page.label=Ɨ́kóɨ fueñe rabemo jaíri
last_page_label=Ɨ́kóɨ fueñe rabemo jaíri
page_rotate_cw.title=Nabene jɨrekai
page_rotate_cw.label=Nabene jɨrekai
page_rotate_cw_label=Nabene jɨrekai
page_rotate_ccw.title=Jarɨ́fene jirekaɨ
page_rotate_ccw.label=Jarɨ́fene jirekaɨ
page_rotate_ccw_label=Jarɨ́fene jirekaɨ


# Document properties dialog box
document_properties_file_name=Ráanɨ mamékɨ:
document_properties_file_size=Ráanɨ dɨeze:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Kúega mámekɨ:
document_properties_author=Fɨnokamɨe:
document_properties_subject=Mɨnɨka:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Fɨnoraɨma:
document_properties_version=Yóga ráfue PDF:
document_properties_close=Ɨ́baide

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
attachments.title=Dájemo jónega akatairi
attachments_label=Dano jónega
thumbs.title=Dúe íya akatairi
thumbs_label=Dúe íya

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Rabe {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Dúe íya rabe {{page}}

# Find panel button title and messages
find_previous_label=Jɨáɨkena\u0020
find_next_label=Báɨfene
find_highlight=Nana rɨgɨno
find_not_found=Daɨna báñeiga

# Error panel labels
error_more_info=Jamano ráfue
error_less_info=Dúe ráfue
error_close=Ɨ́bai
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Úaina: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Pila: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Jónia ráa: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Ida: {{line}}

# Predefined zoom values
page_scale_auto=Zoom dama fɨnode
page_scale_actual=Bírui dɨeze
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Fɨgòñede

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} baítade]
password_ok=Jɨɨ

