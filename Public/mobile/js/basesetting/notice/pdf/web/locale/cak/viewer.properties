# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Jun kan ruxaq
previous_label=Chuwäch
next.title=Jun chik ruxaq
next_label=Jun chik

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ruxaq
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=richin {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} richin {{pagesCount}})

zoom_out.title=Tich'utinirisäx
zoom_out_label=Tich'utinirisäx
zoom_in.title=Tinimirisäx
zoom_in_label=Tinimirisäx
zoom.title=Sum
presentation_mode.title=Tijal ri rub'anikil niwachin
presentation_mode_label=Pa rub'eyal niwachin
open_file.title=Tijaq yakb'äl
open_file_label=Tijaq
print.title=Titz'ajb'äx
print_label=Titz'ajb'äx
download.title=Tiqasäx
download_label=Tiqasäx
bookmark.title=Rutz'etik wakami (tiwachib'ëx o tijaq pa jun k'ak'a' tzuwäch)
bookmark_label=Rutzub'al wakami

# Secondary toolbar and context menu
tools.title=Samajib'äl
tools_label=Samajib'äl
first_page.title=Tib'e pa nab'ey ruxaq
first_page.label=Tib'e pa nab'ey ruxaq
first_page_label=Tib'e pa nab'ey ruxaq
last_page.title=Tib'e pa ruk'isib'äl ruxaq
last_page.label=Tib'e pa ruk'isib'äl ruxaq
last_page_label=Tib'e pa ruk'isib'äl ruxaq
page_rotate_cw.title=Tisutïx pan ajkiq'a'
page_rotate_cw.label=Tisutïx pan ajkiq'a'
page_rotate_cw_label=Tisutïx pan ajkiq'a'
page_rotate_ccw.title=Tisutïx pan ajxokon
page_rotate_ccw.label=Tisutïx pan ajxokon
page_rotate_ccw_label=Tisutïx pan ajxokon

cursor_text_select_tool.title=Titzij ri rusamajib'al Rucha'ik Rucholajem Tzij
cursor_text_select_tool_label=Rusamajib'al Rucha'ik Rucholajem Tzij
cursor_hand_tool.title=Titzij ri q'ab'aj samajib'äl
cursor_hand_tool_label=Q'ab'aj Samajib'äl

scroll_vertical.title=Tokisäx Pa'äl Q'axanem
scroll_vertical_label=Pa'äl Q'axanem
scroll_horizontal.title=Tokisäx Kotz'öl Q'axanem
scroll_horizontal_label=Kotz'öl Q'axanem
scroll_wrapped.title=Tokisäx Tzub'aj Q'axanem
scroll_wrapped_label=Tzub'aj Q'axanem

spread_none.title=Man ketun taq ruxaq pa rub'eyal wuj
spread_none_label=Majun Rub'eyal
spread_odd.title=Ke'atunu' ri taq ruxaq rik'in natikirisaj rik'in jun man k'ulaj ta rajilab'al
spread_odd_label=Man K'ulaj Ta Rub'eyal
spread_even.title=Ke'atunu' ri taq ruxaq rik'in natikirisaj rik'in jun k'ulaj rajilab'al
spread_even_label=K'ulaj Rub'eyal

# Document properties dialog box
document_properties.title=Taq richinil wuj…
document_properties_label=Taq richinil wuj…
document_properties_file_name=Rub'i' yakb'äl:
document_properties_file_size=Runimilem yakb'äl:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=B'i'aj:
document_properties_author=B'anel:
document_properties_subject=Taqikil:
document_properties_keywords=Kixe'el taq tzij:
document_properties_creation_date=Ruq'ijul xtz'uk:
document_properties_modification_date=Ruq'ijul xjalwachïx:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Q'inonel:
document_properties_producer=PDF b'anöy:
document_properties_version=PDF ruwäch:
document_properties_page_count=Jarupe' ruxaq:
document_properties_page_size=Runimilem ri Ruxaq:
document_properties_page_size_unit_inches=pa
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=rupalem
document_properties_page_size_orientation_landscape=rukotz'olem
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Loman wuj
document_properties_page_size_name_legal=Nïm wuj
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Anin Rutz'etik Ajk'amaya'l:
document_properties_linearized_yes=Ja'
document_properties_linearized_no=Mani
document_properties_close=Titz'apïx

print_progress_message=Ruchojmirisaxik wuj richin nitz'ajb'äx…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Tiq'at

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Tijal ri ajxikin kajtz'ik
toggle_sidebar_notification.title=Tik'ex ri ajxikin yuqkajtz'ik (ri wuj eruk'wan taq ruchi'/taqoj taq yakb'äl)
toggle_sidebar_label=Tijal ri ajxikin kajtz'ik
document_outline.title=Tik'ut pe ruch'akulal wuj (kamul-pitz'oj richin nirik'/nich'utinirisäx ronojel ruch'akulal)
document_outline_label=Ruch'akulal wuj
attachments.title=Kek'ut pe taq taqoj
attachments_label=Taq taqoj
thumbs.title=Kek'ut pe taq ch'utiq
thumbs_label=Koköj
findbar.title=Tikanöx chupam ri wuj
findbar_label=Tikanöx

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ruxaq {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Ruch'utinirisaxik ruxaq {{page}}

# Find panel button title and messages
find_input.title=Tikanöx
find_input.placeholder=Tikanöx pa wuj…
find_previous.title=Tib'an b'enam pa ri jun kan q'aptzij xilitäj
find_previous_label=Jun kan
find_next.title=Tib'e pa ri jun chik pajtzij xilitäj
find_next_label=Jun chik
find_highlight=Tiya' retal ronojel
find_match_case_label=Tuk'äm ri' kik'in taq nimatz'ib' chuqa' taq ch'utitz'ib'
find_entire_word_label=Tz'aqät taq tzij
find_reached_top=Xb'eq'i' ri rutikirib'al wuj, xtikanöx k'a pa ruk'isib'äl
find_reached_bottom=Xb'eq'i' ri ruk'isib'äl wuj, xtikanöx pa rutikirib'al
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} richin {{total}} nuk'äm ri'
find_match_count[two]={{current}} richin {{total}} nikik'äm ki'
find_match_count[few]={{current}} richin {{total}} nikik'äm ki'
find_match_count[many]={{current}} richin {{total}} nikik'äm ki'
find_match_count[other]={{current}} richin {{total}} nikik'äm ki'
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=K'ïy chi re {{limit}} nikik'äm ki'
find_match_count_limit[one]=K'ïy chi re {{limit}} nuk'äm ri'
find_match_count_limit[two]=K'ïy chi re {{limit}} nikik'äm ki'
find_match_count_limit[few]=K'ïy chi re {{limit}} nikik'äm ki'
find_match_count_limit[many]=K'ïy chi re {{limit}} nikik'äm ki'
find_match_count_limit[other]=K'ïy chi re {{limit}} nikik'äm ki'
find_not_found=Man xilitäj ta ri pajtzij

# Error panel labels
error_more_info=Ch'aqa' chik rutzijol
error_less_info=Jub'a' ok rutzijol
error_close=Titz'apïx
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Uqxa'n: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Tzub'aj: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Yakb'äl: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=B'ey: {{line}}
rendering_error=Xk'ulwachitäj jun sachoj toq ninuk'wachij ri ruxaq.

# Predefined zoom values
page_scale_width=Ruwa ruxaq
page_scale_fit=Tinuk' ruxaq
page_scale_auto=Yonil chi nimilem
page_scale_actual=Runimilem
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Sachoj
loading_error=\u0020Xk'ulwachitäj jun sach'oj toq xnuk'ux ri PDF .
invalid_file_error=Man oke ta o yujtajinäq ri PDF yakb'äl.
missing_file_error=Man xilitäj ta ri PDF yakb'äl.
unexpected_response_error=Man oyob'en ta tz'olin rutzij ruk'u'x samaj.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Tz'ib'anïk]
password_label=Tatz'ib'aj ri ewan tzij richin najäq re yakb'äl re' pa PDF.
password_invalid=Man okel ta ri ewan tzij: Tatojtob'ej chik.
password_ok=Ütz
password_cancel=Tiq'at

printing_not_supported=Rutzijol k'ayewal: Ri rutz'ajb'axik man koch'el ta ronojel pa re okik'amaya'l re'.
printing_not_ready=Rutzijol k'ayewal: Ri PDF man xusamajij ta ronojel richin nitz'ajb'äx.
web_fonts_disabled=E chupül ri taq ajk'amaya'l tz'ib': man tikirel ta nokisäx ri taq tz'ib' PDF pa ch'ikenïk
document_colors_not_allowed=Ri taq wuj pa PDF man ya'on ta q'ij chi ke richin nikokisaj ri taq kib'onil: “Tiya' q'ij chi ke ri taq ruxaq chi kekicha' ri taq kib'onil” chupun pa ri awokik'amaya'l.
