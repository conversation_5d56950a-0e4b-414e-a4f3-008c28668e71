<?php

namespace Common\Common;

/**
 * Session管理类
 * 用于处理登录时长延长和Session自动续期
 */
class SessionManager
{
    /**
     * 检查并自动延长Session
     * @param int $userId 用户ID
     * @return bool 是否成功延长
     */
    public static function checkAndExtendSession($userId = null)
    {
        if (!$userId) {
            $userId = session('userid');
        }
        
        if (!$userId) {
            return false;
        }
        
        // 检查是否启用自动延长
        if (!C('AUTO_EXTEND_SESSION')) {
            return false;
        }
        
        // 获取当前Session信息
        $sessionInfo = self::getSessionInfo();
        if (!$sessionInfo) {
            return false;
        }
        
        $currentTime = time();
        $expireTime = $sessionInfo['session_expire'];
        $remainingTime = $expireTime - $currentTime;
        $threshold = C('SESSION_EXTEND_THRESHOLD') ?: 604800; // 默认7天
        
        // 如果剩余时间少于阈值，则延长Session
        if ($remainingTime < $threshold && $remainingTime > 0) {
            return self::extendSession($userId);
        }
        
        return true;
    }
    
    /**
     * 延长Session有效期
     * @param int $userId 用户ID
     * @return bool 是否成功
     */
    public static function extendSession($userId)
    {
        $sessionId = session_id();
        if (!$sessionId) {
            return false;
        }
        
        $newExpireTime = time() + C('LOGIN_SESSION_EXPIRE');
        $sessionTable = C('SESSION_TABLE') ?: C("DB_PREFIX") . "session";
        
        // 更新Session表中的过期时间
        $model = new \Think\Model();
        $result = $model->execute(
            "UPDATE {$sessionTable} SET session_expire = {$newExpireTime} WHERE session_id = '{$sessionId}'"
        );
        
        if ($result !== false) {
            // 更新用户表中的登录时间
            $userModel = new \Admin\Model\UserModel();
            $userModel->updateData('user', [
                'logintime' => time(),
                'last_activity' => time()
            ], ['userid' => $userId]);
            
            // 记录Session延长日志
            self::logSessionExtension($userId, $newExpireTime);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取当前Session信息
     * @return array|false Session信息
     */
    public static function getSessionInfo()
    {
        $sessionId = session_id();
        if (!$sessionId) {
            return false;
        }
        
        $sessionTable = C('SESSION_TABLE') ?: C("DB_PREFIX") . "session";
        $model = new \Think\Model();
        
        $result = $model->query(
            "SELECT session_expire, session_data FROM {$sessionTable} WHERE session_id = '{$sessionId}'"
        );
        
        return $result ? $result[0] : false;
    }
    
    /**
     * 设置记住登录Cookie
     * @param array $user 用户信息
     * @param int $expireDays 过期天数，默认90天
     * @return bool 是否成功
     */
    public static function setRememberCookie($user, $expireDays = 90)
    {
        $salt = C('MD5_KEY');
        $identifier = md5($salt . md5($user['usernum'] . md5($salt)));
        $token = md5(uniqid(rand(), true));
        $timeout = time() + ($expireDays * 24 * 3600);
        
        // 设置Cookie
        $cookieValue = "{$identifier}:{$token}";
        $ok = setcookie('auth', $cookieValue, $timeout, '/', '', false, true);
        
        if ($ok) {
            // 保存到数据库
            $userModel = new \Admin\Model\UserModel();
            $upData = [
                'identifier' => $identifier,
                'token' => $token,
                'timeout' => $timeout,
                'remember_token_created' => time()
            ];
            
            $result = $userModel->updateData('user', $upData, ['userid' => $user['userid']]);
            
            if ($result) {
                self::logRememberLogin($user['userid'], $timeout);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 验证记住登录Cookie
     * @return array|false 用户信息或false
     */
    public static function validateRememberCookie()
    {
        if (!isset($_COOKIE['auth'])) {
            return false;
        }
        
        $auth = $_COOKIE['auth'];
        $parts = explode(':', $auth);
        
        if (count($parts) !== 2) {
            self::clearRememberCookie();
            return false;
        }
        
        list($identifier, $token) = $parts;
        
        // 清理输入（使用mysqli_real_escape_string替代已废弃的mysql_real_escape_string）
        $identifier = addslashes($identifier);
        
        // 查询用户信息
        $userModel = new \Admin\Model\UserModel();
        $user = $userModel->DB_get_one('user', '*', ['identifier' => $identifier]);
        
        if (!$user) {
            self::clearRememberCookie();
            return false;
        }
        
        // 验证token
        if ($user['token'] !== $token) {
            self::clearRememberCookie();
            return false;
        }
        
        // 检查是否过期
        if (time() > $user['timeout']) {
            self::clearRememberCookie();
            return false;
        }
        
        // 验证通过，自动延长Cookie
        self::setRememberCookie($user, 90);
        
        return $user;
    }
    
    /**
     * 清除记住登录Cookie
     */
    public static function clearRememberCookie()
    {
        setcookie('auth', '', time() - 3600, '/');
    }
    
    /**
     * 获取Session剩余时间
     * @return int 剩余秒数，-1表示已过期，0表示获取失败
     */
    public static function getSessionRemainingTime()
    {
        $sessionInfo = self::getSessionInfo();
        if (!$sessionInfo) {
            return 0;
        }
        
        $remainingTime = $sessionInfo['session_expire'] - time();
        return max(-1, $remainingTime);
    }
    
    /**
     * 记录Session延长日志
     * @param int $userId 用户ID
     * @param int $newExpireTime 新的过期时间
     */
    private static function logSessionExtension($userId, $newExpireTime)
    {
        $logModel = new \Admin\Model\UserModel();
        $logData = [
            'userid' => $userId,
            'username' => session('username'),
            'action' => 'session_extend',
            'ip' => get_ip(),
            'remark' => 'Session自动延长至' . date('Y-m-d H:i:s', $newExpireTime),
            'action_time' => date('Y-m-d H:i:s'),
            'module' => 'SessionManager',
        ];
        
        $logModel->insertData('operation_log', $logData);
    }
    
    /**
     * 记录记住登录日志
     * @param int $userId 用户ID
     * @param int $expireTime 过期时间
     */
    private static function logRememberLogin($userId, $expireTime)
    {
        $logModel = new \Admin\Model\UserModel();
        $logData = [
            'userid' => $userId,
            'username' => session('username'),
            'action' => 'remember_login',
            'ip' => get_ip(),
            'remark' => '设置记住登录至' . date('Y-m-d H:i:s', $expireTime),
            'action_time' => date('Y-m-d H:i:s'),
            'module' => 'SessionManager',
        ];
        
        $logModel->insertData('operation_log', $logData);
    }
    
    /**
     * 清理过期的Session
     * @return int 清理的数量
     */
    public static function cleanExpiredSessions()
    {
        $sessionTable = C('SESSION_TABLE') ?: C("DB_PREFIX") . "session";
        $model = new \Think\Model();
        
        $result = $model->execute(
            "DELETE FROM {$sessionTable} WHERE session_expire < " . time()
        );
        
        return $result ?: 0;
    }
    
    /**
     * 获取在线用户统计
     * @return array 统计信息
     */
    public static function getOnlineStats()
    {
        $sessionTable = C('SESSION_TABLE') ?: C("DB_PREFIX") . "session";
        $model = new \Think\Model();
        
        // 获取活跃Session数量
        $activeCount = $model->query(
            "SELECT COUNT(*) as count FROM {$sessionTable} WHERE session_expire > " . time()
        );
        
        // 获取今日登录用户数
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayCount = $model->query(
            "SELECT COUNT(DISTINCT userid) as count FROM sb_operation_log 
             WHERE action = '登录系统' AND action_time >= '" . date('Y-m-d 00:00:00') . "'"
        );
        
        return [
            'active_sessions' => $activeCount[0]['count'] ?? 0,
            'today_logins' => $todayCount[0]['count'] ?? 0,
            'session_expire_time' => C('LOGIN_SESSION_EXPIRE'),
            'remember_expire_time' => C('REMEMBER_LOGIN_EXPIRE'),
        ];
    }
}
