<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录时长配置 - {$Think.config.APP_TITLE}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__PUBLIC__/layui/css/layui.css">
    <link rel="stylesheet" href="__PUBLIC__/css/admin.css">
</head>
<body class="admin-page" data-session-monitor="true">
    <div class="layui-container" style="margin-top: 20px;">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>登录时长配置管理</h2>
                    </div>
                    <div class="layui-card-body">
                        
                        <!-- 当前配置信息 -->
                        <fieldset class="layui-elem-field">
                            <legend>当前配置</legend>
                            <div class="layui-field-box">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-md6">
                                        <table class="layui-table">
                                            <tbody>
                                                <tr>
                                                    <td>Session过期时间</td>
                                                    <td id="current-session-expire">30天</td>
                                                </tr>
                                                <tr>
                                                    <td>记住登录过期时间</td>
                                                    <td id="current-remember-expire">90天</td>
                                                </tr>
                                                <tr>
                                                    <td>自动延长Session</td>
                                                    <td id="current-auto-extend">启用</td>
                                                </tr>
                                                <tr>
                                                    <td>延长阈值</td>
                                                    <td id="current-extend-threshold">7天</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="layui-col-md6">
                                        <div class="layui-card">
                                            <div class="layui-card-header">在线统计</div>
                                            <div class="layui-card-body">
                                                <p>活跃Session数: <span id="active-sessions">-</span></p>
                                                <p>今日登录用户: <span id="today-logins">-</span></p>
                                                <button class="layui-btn layui-btn-sm" onclick="refreshStats()">刷新统计</button>
                                                <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="cleanExpiredSessions()">清理过期Session</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        
                        <!-- 配置表单 -->
                        <fieldset class="layui-elem-field">
                            <legend>修改配置</legend>
                            <div class="layui-field-box">
                                <form class="layui-form" id="configForm">
                                    <div class="layui-row layui-col-space15">
                                        <div class="layui-col-md6">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">Session过期时间</label>
                                                <div class="layui-input-inline">
                                                    <input type="number" name="session_expire_days" placeholder="天数" class="layui-input" value="30" min="1" max="90">
                                                </div>
                                                <div class="layui-form-mid layui-word-aux">天 (1-90天)</div>
                                            </div>
                                            
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">记住登录过期时间</label>
                                                <div class="layui-input-inline">
                                                    <input type="number" name="remember_expire_days" placeholder="天数" class="layui-input" value="90" min="1" max="365">
                                                </div>
                                                <div class="layui-form-mid layui-word-aux">天 (1-365天)</div>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-col-md6">
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">自动延长Session</label>
                                                <div class="layui-input-block">
                                                    <input type="checkbox" name="auto_extend" lay-skin="switch" lay-text="启用|禁用" checked>
                                                </div>
                                            </div>
                                            
                                            <div class="layui-form-item">
                                                <label class="layui-form-label">延长阈值</label>
                                                <div class="layui-input-inline">
                                                    <input type="number" name="extend_threshold_days" placeholder="天数" class="layui-input" value="7" min="1" max="30">
                                                </div>
                                                <div class="layui-form-mid layui-word-aux">天 (剩余时间少于此值时自动延长)</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <button class="layui-btn" lay-submit lay-filter="configSubmit">保存配置</button>
                                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </fieldset>
                        
                        <!-- 当前登录状态 -->
                        <fieldset class="layui-elem-field">
                            <legend>我的登录状态</legend>
                            <div class="layui-field-box">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-md8">
                                        <table class="layui-table">
                                            <tbody>
                                                <tr>
                                                    <td>用户名</td>
                                                    <td id="my-username">-</td>
                                                </tr>
                                                <tr>
                                                    <td>最后登录时间</td>
                                                    <td id="my-last-login">-</td>
                                                </tr>
                                                <tr>
                                                    <td>最后活动时间</td>
                                                    <td id="my-last-activity">-</td>
                                                </tr>
                                                <tr>
                                                    <td>Session剩余时间</td>
                                                    <td id="my-remaining-time">-</td>
                                                </tr>
                                                <tr>
                                                    <td>Session过期时间</td>
                                                    <td id="my-expire-date">-</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="layui-col-md4">
                                        <button class="layui-btn layui-btn-normal" onclick="extendMySession()">延长我的登录时间</button>
                                        <button class="layui-btn layui-btn-primary" onclick="refreshMyStatus()">刷新状态</button>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="__PUBLIC__/layui/layui.js"></script>
    <script src="__PUBLIC__/js/session.manager.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            // 页面加载时获取当前状态
            loadCurrentStatus();
            loadOnlineStats();
            
            // 表单提交
            form.on('submit(configSubmit)', function(data){
                var formData = data.field;
                
                // 转换为秒
                var postData = {
                    session_expire: formData.session_expire_days * 24 * 3600,
                    remember_expire: formData.remember_expire_days * 24 * 3600,
                    auto_extend: formData.auto_extend ? 1 : 0,
                    extend_threshold: formData.extend_threshold_days * 24 * 3600
                };
                
                // 发送配置更新请求
                fetch('/A/Login/SessionExtend/setLoginConfig', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(postData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 1) {
                        layer.msg('配置保存成功', {icon: 1});
                        loadCurrentStatus();
                    } else {
                        layer.msg(data.msg || '配置保存失败', {icon: 2});
                    }
                })
                .catch(error => {
                    layer.msg('请求失败', {icon: 2});
                });
                
                return false;
            });
        });
        
        // 加载当前状态
        function loadCurrentStatus() {
            fetch('/A/Login/SessionExtend/getLoginStatus')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 1) {
                        updateStatusDisplay(data.data);
                    }
                });
        }
        
        // 加载在线统计
        function loadOnlineStats() {
            fetch('/A/Login/SessionExtend/getOnlineStats')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 1) {
                        updateStatsDisplay(data.data);
                    }
                });
        }
        
        // 更新状态显示
        function updateStatusDisplay(data) {
            // 更新我的状态
            document.getElementById('my-username').textContent = data.user.username;
            document.getElementById('my-last-login').textContent = data.user.last_login || '-';
            document.getElementById('my-last-activity').textContent = data.user.last_activity || '-';
            document.getElementById('my-remaining-time').textContent = data.session.remaining_days + ' 天';
            document.getElementById('my-expire-date').textContent = data.session.expire_date;
            
            // 更新当前配置
            document.getElementById('current-session-expire').textContent = 
                Math.round(data.session.session_expire_time / 86400) + '天';
            document.getElementById('current-remember-expire').textContent = 
                Math.round(data.session.remember_expire_time / 86400) + '天';
            document.getElementById('current-auto-extend').textContent = 
                data.session.auto_extend_enabled ? '启用' : '禁用';
            document.getElementById('current-extend-threshold').textContent = 
                data.session.extend_threshold_days + '天';
        }
        
        // 更新统计显示
        function updateStatsDisplay(data) {
            document.getElementById('active-sessions').textContent = data.active_sessions;
            document.getElementById('today-logins').textContent = data.today_logins;
        }
        
        // 延长我的Session
        function extendMySession() {
            fetch('/A/Login/SessionExtend/extendSession', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.status === 1) {
                        layer.msg('登录时间延长成功', {icon: 1});
                        loadCurrentStatus();
                    } else {
                        layer.msg(data.msg || '延长失败', {icon: 2});
                    }
                });
        }
        
        // 刷新我的状态
        function refreshMyStatus() {
            loadCurrentStatus();
            layer.msg('状态已刷新', {icon: 1});
        }
        
        // 刷新统计
        function refreshStats() {
            loadOnlineStats();
            layer.msg('统计已刷新', {icon: 1});
        }
        
        // 清理过期Session
        function cleanExpiredSessions() {
            layer.confirm('确定要清理过期的Session吗？', function(index){
                fetch('/A/Login/SessionExtend/cleanExpiredSessions', {method: 'POST'})
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 1) {
                            layer.msg('清理完成，共清理 ' + data.data.cleaned_count + ' 个过期Session', {icon: 1});
                            loadOnlineStats();
                        } else {
                            layer.msg(data.msg || '清理失败', {icon: 2});
                        }
                    });
                layer.close(index);
            });
        }
    </script>
</body>
</html>
