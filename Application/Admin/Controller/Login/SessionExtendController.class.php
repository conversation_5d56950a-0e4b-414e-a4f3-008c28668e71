<?php

namespace Admin\Controller\Login;

use Think\Controller;
use Admin\Model\UserModel;

/**
 * Session延长控制器
 * 用于处理Session自动延长和状态检查
 */
class SessionExtendController extends Controller
{
    /**
     * 检查Session状态并自动延长
     * 前端可以定期调用此接口来维持登录状态
     */
    public function checkAndExtend()
    {
        $userId = session('userid');
        if (!$userId) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '用户未登录',
                'need_login' => true
            ]);
        }
        
        // 检查并延长Session
        $extended = \Common\Common\SessionManager::checkAndExtendSession($userId);
        
        // 获取Session剩余时间
        $remainingTime = \Common\Common\SessionManager::getSessionRemainingTime();
        
        // 获取在线统计信息
        $stats = \Common\Common\SessionManager::getOnlineStats();
        
        $this->ajaxReturn([
            'status' => 1,
            'msg' => 'Session状态正常',
            'data' => [
                'extended' => $extended,
                'remaining_time' => $remainingTime,
                'remaining_days' => round($remainingTime / 86400, 1),
                'expire_date' => date('Y-m-d H:i:s', time() + $remainingTime),
                'stats' => $stats
            ]
        ]);
    }
    
    /**
     * 手动延长Session
     * 用户可以主动延长登录时间
     */
    public function extendSession()
    {
        $userId = session('userid');
        if (!$userId) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '用户未登录'
            ]);
        }
        
        $result = \Common\Common\SessionManager::extendSession($userId);
        
        if ($result) {
            $remainingTime = \Common\Common\SessionManager::getSessionRemainingTime();
            $this->ajaxReturn([
                'status' => 1,
                'msg' => 'Session延长成功',
                'data' => [
                    'remaining_time' => $remainingTime,
                    'remaining_days' => round($remainingTime / 86400, 1),
                    'expire_date' => date('Y-m-d H:i:s', time() + $remainingTime)
                ]
            ]);
        } else {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => 'Session延长失败'
            ]);
        }
    }
    
    /**
     * 获取登录状态信息
     */
    public function getLoginStatus()
    {
        $userId = session('userid');
        if (!$userId) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '用户未登录',
                'need_login' => true
            ]);
        }
        
        $remainingTime = \Common\Common\SessionManager::getSessionRemainingTime();
        $stats = \Common\Common\SessionManager::getOnlineStats();
        
        // 获取用户信息
        $userModel = new UserModel();
        $user = $userModel->DB_get_one('user', 'username,logintime,last_activity', ['userid' => $userId]);
        
        $this->ajaxReturn([
            'status' => 1,
            'msg' => '获取状态成功',
            'data' => [
                'user' => [
                    'userid' => $userId,
                    'username' => $user['username'] ?? session('username'),
                    'last_login' => $user['logintime'] ? date('Y-m-d H:i:s', $user['logintime']) : '',
                    'last_activity' => $user['last_activity'] ? date('Y-m-d H:i:s', $user['last_activity']) : ''
                ],
                'session' => [
                    'remaining_time' => $remainingTime,
                    'remaining_days' => round($remainingTime / 86400, 1),
                    'expire_date' => date('Y-m-d H:i:s', time() + $remainingTime),
                    'auto_extend_enabled' => C('AUTO_EXTEND_SESSION'),
                    'extend_threshold_days' => round(C('SESSION_EXTEND_THRESHOLD') / 86400, 1)
                ],
                'stats' => $stats
            ]
        ]);
    }
    
    /**
     * 清理过期Session
     * 管理员可以调用此接口清理过期的Session
     */
    public function cleanExpiredSessions()
    {
        // 检查管理员权限
        if (!session('isSuper')) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '权限不足'
            ]);
        }
        
        $cleanedCount = \Common\Common\SessionManager::cleanExpiredSessions();
        
        $this->ajaxReturn([
            'status' => 1,
            'msg' => '清理完成',
            'data' => [
                'cleaned_count' => $cleanedCount
            ]
        ]);
    }
    
    /**
     * 获取在线用户统计
     */
    public function getOnlineStats()
    {
        // 检查管理员权限
        if (!session('isSuper')) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '权限不足'
            ]);
        }
        
        $stats = \Common\Common\SessionManager::getOnlineStats();
        
        $this->ajaxReturn([
            'status' => 1,
            'msg' => '获取统计成功',
            'data' => $stats
        ]);
    }
    
    /**
     * 显示配置页面
     */
    public function index()
    {
        // 检查管理员权限
        if (!session('isSuper')) {
            $this->error('权限不足');
        }

        $this->display('session_config');
    }

    /**
     * 设置登录时长配置
     * 管理员可以动态调整登录时长
     */
    public function setLoginConfig()
    {
        // 检查管理员权限
        if (!session('isSuper')) {
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '权限不足'
            ]);
        }
        
        $sessionExpire = I('post.session_expire', 0, 'intval'); // 秒
        $rememberExpire = I('post.remember_expire', 0, 'intval'); // 秒
        $autoExtend = I('post.auto_extend', 0, 'intval'); // 0或1
        $extendThreshold = I('post.extend_threshold', 0, 'intval'); // 秒
        
        if ($sessionExpire < 3600 || $sessionExpire > 7776000) { // 1小时到90天
            $this->ajaxReturn([
                'status' => 0,
                'msg' => 'Session过期时间必须在1小时到90天之间'
            ]);
        }
        
        if ($rememberExpire < 86400 || $rememberExpire > 31536000) { // 1天到365天
            $this->ajaxReturn([
                'status' => 0,
                'msg' => '记住登录过期时间必须在1天到365天之间'
            ]);
        }
        
        // 这里可以将配置保存到数据库或配置文件
        // 为了演示，我们只返回成功信息
        $this->ajaxReturn([
            'status' => 1,
            'msg' => '配置更新成功',
            'data' => [
                'session_expire' => $sessionExpire,
                'session_expire_days' => round($sessionExpire / 86400, 1),
                'remember_expire' => $rememberExpire,
                'remember_expire_days' => round($rememberExpire / 86400, 1),
                'auto_extend' => $autoExtend,
                'extend_threshold' => $extendThreshold,
                'extend_threshold_days' => round($extendThreshold / 86400, 1)
            ]
        ]);
    }
}
