<?php

namespace Admin\Controller\Login;

use Admin\Model\ApproveProcessModel;
use Admin\Model\AssetsInfoModel;
use Admin\Model\CategoryModel;
use Admin\Model\DepartmentModel;
use Admin\Model\UserModel;
use Think\Controller;

class CheckLoginController extends Controller
{
    public function __construct()
    {
        //调用父类构造
        parent::__construct();
        if (!session('userid')) {
            redirect(U("A/Login/login"));
        } else {
            //G('begin');
            //权限判断 整体耗时大概0.04s
            $userid   = session('userid');
            $isSuper  = session('isSuper');
            $model    = new UserModel();
            $mc       = explode('/', CONTROLLER_NAME);
            $self     = explode('/', trim(get_url(), '/'));
            $loginUrl = '/' . $self[0] . '/Login/login';
            //判断账户是否已过期
            $expire_time = $model->DB_get_one('user', 'expire_time', ['userid' => $userid]);
            if ($expire_time['expire_time'] && time() >= strtotime($expire_time['expire_time'])) {
                session(null);
                $this->assign('jumpUrl', $loginUrl);
                $this->assign('errmsg', '您的账户已过期，如需继续使用，请联系管理员设置！');
                $this->display('Public/error');
                exit;
            }

            // 检查并自动延长Session
            \Common\Common\SessionManager::checkAndExtendSession($userid);
            if (!in_array($mc[0], ['Login'])) {
                //查询模块是否已关闭
                $module_status = $model->DB_get_one('menu', 'menuid,status',
                    $where = ['name' => $mc[0], 'parentid' => 0]);
                if ($module_status['status'] != 1) {
                    session(null);
                    $this->assign('jumpUrl', $loginUrl);
                    $this->assign('errmsg', '该模块已关闭，请重新登录系统！');
                    $this->display('Public/error');
                    exit;
                }
            }

            //不做权限验证的方法列表
            $noCheck = [
                'Login'            => [
                    'Index' => [
                        'index',//框架加载js css文件
                        'target',//框架主页
                        'taskList',//计划列表
                        'layout',//框架上面按钮功能部分
                    ],
                    'login' => [
                        'getMenus',//获取菜单
                    ],
                ],
                'Assets'           => [
                    'Assets'   => [
                        'index',//尚不明确
                    ],
                    'Transfer' => [
                        'prin',//尚不明确
                    ],
                    'Lookup'   => [
                        'index',
                        'showAssets',//显示设备详情
                        'assetsLifeList',//获取菜单
                    ],
                ],
                'Repair'           => [
                    'Repair'       => [
                        'index',//尚不明确
                        'uploadFile',//上传维修文件
                        'showRepairDetails',//上传维修文件
                        'repairForm',//开挂模式 直接填维修单 忽略所有流程
                    ],
                    'RepairSearch' => [
                        'getRepairSearchList',//维修记录列表
                        'showUpload',//显示上传文件
                        'showRepair',//维修单详情
                    ],
                ],
                'Patrol'           => [
                    'Patrol' => [
                        'index',//尚不明确
                        'allocationPlan',//确认计划
                        'showPlans',//
                        'scanPic',
                        'addAssets',//修订计划添加设备
                        'deletePatrol',//删除计划添加设备
                    ],
                ],
                'Qualities'        => [
                    'Quality' => [
                        'scanTemplate',//质控计划模板预览
                        'showDetail',//查看录入明细
                        'scanPic',
                        'index',
                    ],
                ],
                'Purchases'        => [
                    'PurchasePlans' => [
                        'index',
                    ],
                ],
                'Metering'         => [
                    'Metering' => [
                        'index',
                    ],
                ],
                'Adverse'          => [
                    'Adverse' => [
                        'index',
                    ],
                ],
                'Statistics'       => [
                    'PurchasesStatis' => [
                        'index',
                    ],
                ],
                'OfflineSuppliers' => [
                    'OfflineSuppliers' => [
                        'index',
                    ],
                ],
                'Benefit'          => [
                    'Benefit' => [
                        'assetsBenefitData',
                        'departmentBenefitData',
                        'index',
                    ],
                ],
                'BaseSetting'      => [
                    'IntegratedSetting' => [
                        'index',
                    ],
                    'User'              => [
                        'userInfo',//用户信息
                    ],
                ],
            ];
            if (in_array(ACTION_NAME, $noCheck[$mc[0]][$mc[1]])) {
                return true;
            }
            if (session('password')) {
                $this->redirect('/A/User/userInfo');
                exit;
            }
            if ($isSuper) {
                //超级管理员，不做权限判断
                return true;
            }
            //获取Controller id
            $controllerId = $model->DB_get_one('menu', 'GROUP_CONCAT(menuid) AS menuid',
                $where = ['name' => $mc[1], 'status' => 1]);

            $menuWhere['name']     = ['EQ', ACTION_NAME];
            $menuWhere['status']   = ['EQ', 1];
            $menuWhere['parentid'] = ['IN', $controllerId['menuid']];
            //获取当前要访问的url的menuid
            $menuid = $model->DB_get_one('menu', 'menuid', $menuWhere);
            if (!$menuid['menuid']) {
                $this->assign('jumpUrl', $loginUrl);
                $this->assign('errmsg', '该方法不存在！');
                $this->display('Public/error');
                exit;
            }
            if (session('is_supplier') == C('YES_STATUS')) {
                //厂商用户 验证是否在在允许操作的方法
                if (in_array(ACTION_NAME, C('IS_SUPPLIER_MENU'))) {
                    return true;
                } else {
                    session(null);
                    header("Content-type: text/html; charset=utf-8");
                    $this->assign('jumpUrl', $loginUrl);
                    $this->assign('errmsg', '您没有权限访问此页面！');
                    $this->display('Public/error');
                    exit;
                }
            } else {
                //获取用户所有可以访问的menuid
                $join[0]                          = ' LEFT JOIN __USER_ROLE__ ON __ROLE_MENU__.roleid = __USER_ROLE__.roleid';
                $join[1]                          = ' LEFT JOIN __MENU__ ON __ROLE_MENU__.menuid = __MENU__.menuid';
                $fields                           = 'sb_role_menu.menuid';
                $rolewhere['sb_user_role.userid'] = $userid;
                $rolewhere['sb_menu.status']      = C('YES_STATUS');
                if (C('IS_OPEN_BRANCH') && !C('CAN_MANAGER_BANCH')) {
                    //开启了分院功能，且不可以对分院进行具体管理操作，只能查看
                    if (session('current_hospitalid') != session('job_hospitalid')) {
                        //当前医院ID不等于自己所在工作医院ID，即现在切换到了用户管理的分院，那么所有具体操作权限都不能操作
                        $rolewhere['sb_menu.leftShow'] = C('YES_STATUS');
                    }
                }
                $menuArr = $model->DB_get_all_join('role_menu', 'sb_role_menu', $fields, $join, $rolewhere,
                    'sb_role_menu.menuid', 'sb_role_menu.menuid asc', '');
                $arr     = [];
                foreach ($menuArr as $v) {
                    $arr[] = (int)$v['menuid'];
                }
                //G('end');
                //echo G('begin','end').'s---';
                //判断要访问的menuid是否在$arr中
                if (in_array($menuid['menuid'], $arr)) {
                    return true;
                } else {
                    session(null);
                    header("Content-type: text/html; charset=utf-8");
                    $this->assign('jumpUrl', $loginUrl);
                    $this->assign('errmsg', '您没有权限访问此页面！');
                    $this->display('Public/error');
                    exit;
                }
            }
        }
    }

    protected function _initialize()
    {
        $this->assign('menu_1', session('menu_1'));
        $this->assign('menu_2', session('menu_2'));
        $this->assign('menu_3', session('menu_3'));
        $this->assign('task', session('taskResult'));
        $this->assign('taskNum', session('taskCount'));
        $this->assign('indexTask', session('indexResult'));
    }

    public function checkstatus($value, $text)
    {
        if (!$value) {
            $this->ajaxReturn(['status' => -1, 'msg' => $text], 'json');
        }
    }

    /**
     * 获取部门列表
     *
     * @param $departids array 要获取的departid
     *
     * @return array
     * */
    public function getDepartname($departids = '')
    {
        $departname  = $this->getSelectDepartments();
        $departments = [];
        $i           = 0;
        foreach ($departname as $k => $v) {
            if ($departids) {
                if (in_array($v['departid'], $departids) or session('isSuper') == 1) {
                    $departments[$i]['departid']   = $v['departid'];
                    $departments[$i]['department'] = $v['department'];
                    $i++;
                }
            } else {
                $departments[$i]['departid']   = $v['departid'];
                $departments[$i]['department'] = $v['department'];
                $i++;
            }
        }
        return $departments;
    }

    //公共更新统计部门和分类方法
    public function updateAssetsNumAndTotalPrice()
    {
        $asModel   = new AssetsInfoModel();
        $deModel   = new DepartmentModel();
        $cateModel = new CategoryModel();
        //更新部门表中每个部门的设备数量和总价
        $departCount = $asModel->DB_get_all('assets_info',
            'departid,sum(buy_price) as totalPrice,count(departid) as assetsNum', '', 'departid', '');
        foreach ($departCount as $k => $v) {
            $departData['assetssum']   = $v['assetsNum'];
            $departData['assetsprice'] = $v['totalPrice'];
            $departWhere['departid']   = $v['departid'];
            $deModel->updateData('department', $departData, $departWhere);
        }
        //更新分类表中每个分类的设备数量和总价
        $cateCount = $asModel->DB_get_all('assets_info', 'catid,sum(buy_price) as totalPrice,count(catid) as assetsNum',
            '', 'catid', '');
        foreach ($cateCount as $k => $v) {
            $cateData['assetssum']   = $v['assetsNum'];
            $cateData['assetsprice'] = $v['totalPrice'];
            $cateWhere['catid']      = $v['catid'];
            $cateModel->updateData('category', $cateData, $cateWhere);
        }
        //获取父分类信息
        $cateParent = $asModel->DB_get_one('category', 'group_concat(catid) as ids',
            ['parentid' => 0, 'is_delete' => 0]);
        if ($cateParent['ids']) {
            $parentcount = $asModel->DB_get_all('category',
                'parentid,group_concat(catid order by catid asc) as ids,count(catid) as childNum,sum(assetssum) as assetsNum,sum(assetsprice) as totalPrice',
                ['parentid' => ['IN', $cateParent['ids']], 'is_delete' => 0], 'parentid', '');
            foreach ($parentcount as $k => $v) {
                $parentData['assetssum']   = $v['assetsNum'];
                $parentData['assetsprice'] = $v['totalPrice'];
                $parentData['child']       = $v['childNum'];
                $parentData['arrchildid']  = json_encode(explode(',', $v['ids']));
                $parentWhere['catid']      = $v['parentid'];
                $cateModel->updateData('category', $parentData, $parentWhere);
            }
        }
    }


    /*列表页链接拼接按钮
     * $name 按钮名字
     * $url  按钮链接
     * $class 样式名称
     * $color  字体颜色
     * $type 可加参数
     * */
    public function returnButtonLink($name, $url, $class, $color = '', $type = '', $title = '')
    {
        return '<button style="color:' . $color . ';" class=" ' . $class . '"  ' . $type . ' data-url="' . $url . '" title="' . $title . '">' . $name . '</button>';
    }


    public function returnListLink($name, $url, $layEvent, $class, $style = '', $string = '')
    {
        return '<button class="layui-btn ' . $class . '" lay-event="' . $layEvent . '" style="' . $style . '"  data-url="' . $url . '" ' . $string . '>' . $name . '</button>';
    }

    /**
     * Notes:查询审批是否已开启
     *
     * @param string $type 类型
     *
     * @return bool
     */
    public function checkApproveIsOpen($type, $hospital_id)
    {
        $apModel               = new ApproveProcessModel();
        $where['approve_type'] = ['EQ', $type];
        $where['hospital_id']  = ['EQ', $hospital_id];
        $where['status']       = ['EQ', C('OPEN_STATUS')];
        $res                   = $apModel->DB_get_one('approve_type', '*', $where);
        return $res;
    }

    /**
     * Notes: 获取搜索框下拉科室选择的数据
     * @params $cusWhere array/string 自定义搜索条件
     *
     * @return mixed
     */
    public function getSelectDepartments($cusWhere = [])
    {
        if ($cusWhere) {
            $where = $cusWhere;
        } else {

            $where['hospital_id'] = ['in', session('current_hospitalid')];

        }
        $where['departid']  = ['IN', session('departid')];
        $where['is_delete'] = C('NO_STATUS');
        //获取所有部门
        $departModel = new DepartmentModel();
        return $departModel->DB_get_all('department', 'departid,hospital_id,department,address,assetsrespon', $where,
            '', 'hospital_id asc');
    }

    /**
     * Notes: 获取医院名称
     *
     * @param $hospital_id
     */
    public function get_hospital_name($hospital_id)
    {
        $hosidarr = explode(',', $hospital_id);
        if (count($hosidarr) > 1) {
            $res = ['hospital_name' => '各医院'];
        } else {
            $departModel = new DepartmentModel();
            $res         = $departModel->DB_get_one('hospital', '*', ['hospital_id' => $hospital_id]);
        }
        return $res;
    }


    /**
     * 根据需求获取渲染列表
     *
     * 1 << 1 科室列表
     * 1 << 2 设备字典
     * 1 << 3 财务分类
     *
     * 1<<1 |1<<2 |1<<3 = 14
     *
     * @param $hospital_id
     * @param $feat_num
     *
     * @return void
     */
    public function dicAssign($hospital_id, $feat_num = 14)
    {
        $assetsInfoModel = new AssetsInfoModel();
        $departmentWhere['hospital_id'] = $hospital_id;
        $dic_where['hospital_id']       = $hospital_id;
        $dic_where['status']            = C('OPEN_STATUS');

        // 科室列表
        if (has_feature(1 << 1, $feat_num)) {
            $department = $this->getSelectDepartments($departmentWhere);
            $this->assign('department', str_json_encode($department));
        }
        // 设备字典
        if (has_feature(1 << 2, $feat_num)) {
            $dic_assets = $assetsInfoModel->DB_get_all('dic_assets',
                'assets,catid,assets_category,unit', $dic_where);
            $this->assign('dicAssets', str_json_encode($dic_assets));
        }

        //财务分类
        if (has_feature(1 << 3, $feat_num)) {
            $assets_finance = $assetsInfoModel->getBaseSettingAssets('assets_finance');
            $this->assign('assetsFinance', str_json_encode($assets_finance));
        }
    }
}